# 时间段切换功能实现文档

## 功能概述

为仪表盘1和仪表盘2组件添加了完整的实时跨时间段切换处理逻辑，解决了以下问题：

1. 当前是工作时间段，下一秒是休息时间段时，没有正确处理实时跨时间段的情况
2. 如果处于摸鱼状态，应该自动结束摸鱼
3. 实时收入应该正确更新
4. 中间的切换工作摸鱼按钮也应该变化成下一个时间段的状态

## 实现方案

### 1. 数据结构扩展

在两个仪表盘组件的 `data` 中添加了时间段状态跟踪字段：

```javascript
// 时间段切换检测
lastSegmentId: null,        // 上一个时间段ID
lastSegmentType: null,      // 上一个时间段类型
lastIsInWorkTime: null,     // 上一次是否在工作时间
lastIsInAnySegment: null    // 上一次是否在任何时间段内
```

### 2. 核心方法实现

#### 2.1 时间段变化检测
- `getCurrentTimeSegment(now)`: 获取当前时间段信息
- `detectTimeSegmentChange(now)`: 检测当前时间段是否发生变化

#### 2.2 时间段切换处理
- `handleTimeSegmentTransition(previousState, currentState, now)`: 处理时间段切换的核心逻辑
- `shouldAutoEndFishingOnTransition(previousState, currentState)`: 判断是否需要自动结束摸鱼
- `autoEndFishingOnTransition(now)`: 在时间段切换时自动结束摸鱼

### 3. 集成到现有流程

修改了 `updateCurrentTime` 方法，在每次时间更新时：
1. 检测时间段变化
2. 如果有变化，执行切换处理逻辑
3. 如果没有变化，执行正常的更新流程

## 处理的场景

### 1. 工作时间段到休息时间段
- 自动结束摸鱼（如果正在摸鱼）
- 更新工作摸鱼按钮状态
- 重新计算实时收入

### 2. 休息时间段到工作时间段
- 更新工作摸鱼按钮状态为可用
- 启动实时收入计算

### 3. 有时间段到无时间段
- 自动结束摸鱼（如果正在摸鱼）
- 停止实时收入计算
- 更新界面状态

### 4. 无时间段到有时间段
- 根据新时间段类型更新界面
- 如果是工作时间段，启动实时收入计算

### 5. 跨日期时间段处理
- 仪表盘1支持跨日期时间段的切换检测
- 正确处理从昨天数据到今天数据的切换

## 技术细节

### 1. 状态跟踪
使用四个字段跟踪时间段状态变化：
- `lastSegmentId`: 时间段唯一标识
- `lastSegmentType`: 时间段类型（work/rest/overtime）
- `lastIsInWorkTime`: 是否在工作时间
- `lastIsInAnySegment`: 是否在任何时间段内

### 2. 变化检测
通过对比当前状态和上一次状态，检测以下变化：
- 时间段ID变化
- 时间段类型变化
- 工作时间状态变化
- 时间段存在状态变化

### 3. 摸鱼自动结束
在以下情况下自动结束摸鱼：
- 从工作时间段切换到休息时间段
- 从工作时间段切换到无时间段
- 从任何时间段切换到无时间段

### 4. 实时收入更新
- 时间段切换时重新启动实时收入计算
- 确保使用正确的时间段和时薪信息
- 延迟100ms启动，确保摸鱼状态已更新

## 组件差异

### 仪表盘1 (Dashboard1)
- 支持跨日期时间段处理
- 集成了更复杂的工作状态管理
- 包含工作摸鱼按钮状态更新
- 支持统一信息显示更新

### 仪表盘2 (Dashboard2)
- 专注于实时收入显示
- 简化的时间段检测逻辑
- 主要处理摸鱼自动结束和收入更新

## 测试建议

1. **时间段边界测试**：
   - 在时间段切换的前后几秒进行测试
   - 验证摸鱼状态是否正确结束
   - 验证界面状态是否及时更新

2. **摸鱼状态测试**：
   - 在工作时间段开始摸鱼
   - 等待切换到休息时间段
   - 验证摸鱼是否自动结束

3. **跨日期测试**：
   - 测试跨日期工作时间段的切换
   - 验证从昨天数据到今天数据的切换

4. **实时收入测试**：
   - 验证时间段切换后实时收入计算是否正确
   - 验证时薪是否使用了正确的时间段信息

## 注意事项

1. **性能考虑**：时间段检测在每秒执行，已优化为轻量级操作
2. **状态同步**：确保两个仪表盘组件的状态保持同步
3. **错误处理**：添加了适当的错误处理和日志记录
4. **向后兼容**：不影响现有功能的正常使用
